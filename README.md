# TailsBot - WhatsApp Number Verifier

🤖 **TailsBot** é uma ferramenta automatizada para verificar se números de telefone possuem WhatsApp ativo. Utilizando automação de interface gráfica, o bot verifica uma lista de números e gera relatórios em Excel com os resultados.

## 🚀 Funcionalidades

- ✅ Verificação automática de números de WhatsApp
- 📊 Geração de relatórios em Excel
- 🔄 Processamento em lote com salvamento automático
- 📝 Sistema de logging detalhado
- ⚙️ Configurações personalizáveis
- 🛡️ Tratamento robusto de erros
- 💾 Recuperação de progresso em caso de interrupção

## 📋 Pré-requisitos

- Python 3.7 ou superior
- Windows (devido ao uso de pyautogui)
- Navegador web instalado
- WhatsApp Web configurado no navegador

## 🔧 Instalação

1. **Clone o repositório:**
```bash
git clone https://github.com/mrflag/TailsBot.git
cd TailsBot
```

2. **Crie um ambiente virtual (recomendado):**
```bash
python -m venv venv
venv\Scripts\activate  # Windows
```

3. **Instale as dependências:**
```bash
pip install -r requirements.txt
```

## ⚙️ Configuração

1. **Configure seus números de telefone:**
   - Edite o arquivo `config.py`
   - Substitua a lista `EXAMPLE_PHONE_NUMBERS` pelos seus números
   - Os números devem estar no formato internacional (ex: 5521999999999)

2. **Ajuste as configurações (opcional):**
   - Tempos de espera
   - Confiança de detecção de imagem
   - Nomes de arquivos de saída
   - Configurações de logging

## 🎯 Como Usar

1. **Primeira execução (Calibração automática):**
   - Execute: `python VerifyWhatsappNumber.py`
   - O sistema detectará que não está calibrado
   - Siga as instruções de calibração (10 segundos para posicionar o cursor)
   - A calibração é feita apenas uma vez por máquina/resolução

2. **Configurar números:**
   - Edite o arquivo `numeros_exemplo.txt`
   - Adicione seus números no formato internacional

3. **Executar verificação:**
   - Execute: `python VerifyWhatsappNumber.py`
   - O programa usará a calibração salva automaticamente

4. **Resultados:**
   - Arquivo Excel: `resultados_whatsapp.xlsx`
   - Aba "Válidos": números com WhatsApp
   - Aba "Inválidos": números sem WhatsApp

## 📁 Estrutura do Projeto

```
TailsBot/
├── VerifyWhatsappNumber.py    # Código principal
├── config.py                  # Configurações
├── requirements.txt           # Dependências Python
├── WhatsappOKButton.png      # Imagem do botão OK
├── README.md                 # Documentação
├── .gitignore               # Arquivos ignorados pelo Git
├── resultados_whatsapp.xlsx # Resultados (gerado automaticamente)
└── whatsapp_verifier.log   # Logs (gerado automaticamente)
```

## 🔧 Configurações Avançadas

### Arquivo `config.py`

```python
# Tempos de espera (em segundos)
WAIT_TIME_AFTER_OPEN = 3
WAIT_TIME_BETWEEN_ATTEMPTS = 0.5

# Detecção de imagem
IMAGE_CONFIDENCE = 0.85
MAX_ATTEMPTS = 2

# Arquivo de saída
OUTPUT_FILE = 'resultados_whatsapp.xlsx'
SAVE_INTERVAL = 500  # Salvar a cada X números
```

## 🐛 Solução de Problemas

### Problemas Comuns

1. **Botão OK não detectado:**
   - Verifique se a imagem `WhatsappOKButton.png` está correta
   - Ajuste o valor `IMAGE_CONFIDENCE` no config.py
   - Certifique-se de que o WhatsApp Web está carregado

2. **Erro de permissão:**
   - Execute como administrador
   - Verifique se o pyautogui tem permissões de controle da tela

3. **Números não processados:**
   - Verifique o formato dos números (internacional)
   - Confirme se há conexão com a internet

### Logs e Debugging

- Logs detalhados em `whatsapp_verifier.log`
- Ajuste o nível de log em `config.py`
- Use `LOG_ENABLED = True` para logs completos

## 🤝 Contribuindo

1. Faça um fork do projeto
2. Crie uma branch para sua feature (`git checkout -b feature/AmazingFeature`)
3. Commit suas mudanças (`git commit -m 'Add some AmazingFeature'`)
4. Push para a branch (`git push origin feature/AmazingFeature`)
5. Abra um Pull Request

## 📄 Licença

Este projeto está sob a licença MIT. Veja o arquivo `LICENSE` para mais detalhes.

## ⚠️ Aviso Legal

- Use esta ferramenta de forma responsável
- Respeite os termos de uso do WhatsApp
- Não use para spam ou atividades maliciosas
- O autor não se responsabiliza pelo uso inadequado da ferramenta

## 👨‍💻 Autor

**mrflag**
- GitHub: [@mrflag](https://github.com/mrflag)
- Projeto: [TailsBot](https://github.com/mrflag/TailsBot)

## 🙏 Agradecimentos

- Comunidade Python
- Desenvolvedores das bibliotecas utilizadas
- Contribuidores do projeto

---

⭐ Se este projeto foi útil para você, considere dar uma estrela no GitHub!
