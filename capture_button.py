"""
Script para capturar a imagem do botão OK do WhatsApp
Execute este script quando o botão OK estiver visível na tela
"""

import pyautogui
import time
import os

def capture_ok_button():
    """Captura a imagem do botão OK do WhatsApp"""
    
    print("=== Captura do Botão OK do WhatsApp ===")
    print()
    print("INSTRUÇÕES:")
    print("1. Abra o WhatsApp Web no seu navegador")
    print("2. Digite um número que NÃO possui WhatsApp")
    print("3. Aguarde aparecer o botão 'OK' na tela")
    print("4. Volte para este programa e pressione Enter")
    print()
    
    input("Pressione Enter quando o botão OK estiver visível na tela...")
    
    print("Aguarde 3 segundos para posicionar o mouse...")
    for i in range(3, 0, -1):
        print(f"{i}...")
        time.sleep(1)
    
    print("Clique e arraste para selecionar a área do botão OK!")
    print("Pressione ESC para cancelar")
    
    try:
        # Permite ao usuário selecionar uma região da tela
        region = pyautogui.screenshot()
        
        # Salva a screenshot temporária
        temp_path = "temp_screenshot.png"
        region.save(temp_path)
        
        print(f"Screenshot salva temporariamente em: {temp_path}")
        print()
        print("Agora você precisa recortar manualmente a imagem do botão OK")
        print("e salvar como 'WhatsappOKButton.png'")
        print()
        print("Dicas para recortar:")
        print("- Use um editor de imagem (Paint, GIMP, etc.)")
        print("- Recorte apenas a área do botão OK")
        print("- Salve como PNG com o nome exato: WhatsappOKButton.png")
        print("- Coloque o arquivo no diretório do projeto")
        
    except Exception as e:
        print(f"Erro ao capturar: {e}")

def test_current_button():
    """Testa se a imagem atual do botão funciona"""
    
    button_path = "WhatsappOKButton.png"
    
    if not os.path.exists(button_path):
        print(f"Arquivo {button_path} não encontrado!")
        return
    
    print("Testando detecção do botão atual...")
    
    try:
        # Tenta localizar o botão na tela
        location = pyautogui.locateOnScreen(button_path, confidence=0.8)
        
        if location:
            print(f"✓ Botão encontrado em: {location}")
            
            # Mostra onde o botão foi encontrado
            center_x, center_y = pyautogui.center(location)
            print(f"Centro do botão: ({center_x}, {center_y})")
            
            # Move o mouse para mostrar a localização (sem clicar)
            pyautogui.moveTo(center_x, center_y, duration=1)
            print("Mouse movido para a posição do botão (sem clicar)")
            
        else:
            print("✗ Botão não encontrado na tela atual")
            
    except Exception as e:
        print(f"Erro ao testar: {e}")

def main():
    """Menu principal"""
    
    while True:
        print("\n=== Utilitário de Captura do Botão OK ===")
        print("1. Capturar nova imagem do botão")
        print("2. Testar imagem atual")
        print("3. Sair")
        print()
        
        choice = input("Escolha uma opção (1-3): ").strip()
        
        if choice == "1":
            capture_ok_button()
        elif choice == "2":
            test_current_button()
        elif choice == "3":
            print("Saindo...")
            break
        else:
            print("Opção inválida!")

if __name__ == "__main__":
    main()
