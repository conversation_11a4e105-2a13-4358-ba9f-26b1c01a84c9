"""
Script para calibrar a posição exata do clique
"""

import pyautogui
import time
import webbrowser
import os

def test_current_detection():
    """Testa onde a detecção atual está encontrando"""
    
    print("=== Calibração da Posição do Clique ===")
    print()
    print("Vamos abrir o WhatsApp e calibrar a posição exata")
    print()
    
    # Abre WhatsApp
    test_number = "5521965560787"
    wa_link = f"https://wa.me/{test_number}"
    
    print(f"Abrindo: {wa_link}")
    webbrowser.open(wa_link)
    
    print("Aguardando WhatsApp carregar...")
    time.sleep(8)
    
    # Procura o botão
    image_path = "WhatsappOKButton.png"
    
    if not os.path.exists(image_path):
        print(f"❌ Arquivo {image_path} não encontrado!")
        return
    
    print("Procurando botão...")
    
    try:
        location = pyautogui.locateOnScreen(image_path, confidence=0.6)
        
        if location:
            center_x, center_y = pyautogui.center(location)
            print(f"✓ Botão detectado!")
            print(f"  Área: {location}")
            print(f"  Centro calculado: ({center_x}, {center_y})")
            
            # Move mouse para a posição detectada
            print("Movendo mouse para posição detectada...")
            pyautogui.moveTo(center_x, center_y, duration=1)
            
            print()
            print("O mouse está na posição correta do botão OK?")
            resposta = input("Digite: s (sim), n (não), ou coordenadas x,y (ex: 790,525): ").strip().lower()
            
            if resposta == 's':
                print("🎉 Perfeito! A detecção está correta!")
                
                # Testa o clique
                print("Testando clique...")
                pyautogui.click(center_x, center_y)
                
                time.sleep(2)
                
                # Verifica se funcionou
                try:
                    still_there = pyautogui.locateOnScreen(image_path, confidence=0.6)
                    if not still_there:
                        print("🎉 SUCESSO! Clique funcionou - botão desapareceu!")
                    else:
                        print("⚠ Botão ainda visível")
                except:
                    print("🎉 PROVÁVEL SUCESSO! Botão não encontrado")
                
                return True
                
            elif resposta == 'n':
                print("Vamos ajustar a posição...")
                return calibrate_manual_position(center_x, center_y)
                
            elif ',' in resposta:
                try:
                    new_x, new_y = map(int, resposta.split(','))
                    print(f"Testando posição manual: ({new_x}, {new_y})")
                    
                    pyautogui.moveTo(new_x, new_y, duration=1)
                    
                    confirma = input("Posição correta agora? (s/n): ").strip().lower()
                    if confirma == 's':
                        print("Testando clique na posição manual...")
                        pyautogui.click(new_x, new_y)
                        
                        time.sleep(2)
                        
                        try:
                            still_there = pyautogui.locateOnScreen(image_path, confidence=0.6)
                            if not still_there:
                                print("🎉 SUCESSO! Clique manual funcionou!")
                                
                                # Calcula o offset
                                offset_x = new_x - center_x
                                offset_y = new_y - center_y
                                print(f"OFFSET necessário: x+{offset_x}, y+{offset_y}")
                                print("Anote esses valores para ajustar o código!")
                                
                            else:
                                print("⚠ Botão ainda visível")
                        except:
                            print("🎉 PROVÁVEL SUCESSO! Botão não encontrado")
                        
                        return True
                    
                except ValueError:
                    print("Formato inválido. Use: x,y (ex: 790,525)")
            
        else:
            print("✗ Botão não encontrado")
            
    except Exception as e:
        print(f"Erro: {e}")
    
    return False

def calibrate_manual_position(start_x, start_y):
    """Calibração manual com setas"""
    
    current_x, current_y = start_x, start_y
    
    print()
    print("=== Calibração Manual ===")
    print("Use as teclas:")
    print("w/s - mover para cima/baixo")
    print("a/d - mover para esquerda/direita")
    print("+ - aumentar movimento (5 pixels)")
    print("- - diminuir movimento (1 pixel)")
    print("t - testar clique")
    print("q - sair")
    print()
    
    step = 3  # pixels por movimento
    
    while True:
        print(f"Posição atual: ({current_x}, {current_y}) - Passo: {step}px")
        pyautogui.moveTo(current_x, current_y, duration=0.2)
        
        cmd = input("Comando: ").strip().lower()
        
        if cmd == 'w':
            current_y -= step
        elif cmd == 's':
            current_y += step
        elif cmd == 'a':
            current_x -= step
        elif cmd == 'd':
            current_x += step
        elif cmd == '+':
            step = min(step + 1, 10)
            print(f"Passo aumentado para {step}px")
        elif cmd == '-':
            step = max(step - 1, 1)
            print(f"Passo diminuído para {step}px")
        elif cmd == 't':
            print("Testando clique...")
            pyautogui.click(current_x, current_y)
            
            time.sleep(2)
            
            funcionou = input("Clique funcionou? (s/n): ").strip().lower()
            if funcionou == 's':
                offset_x = current_x - start_x
                offset_y = current_y - start_y
                print(f"🎉 SUCESSO!")
                print(f"Posição correta: ({current_x}, {current_y})")
                print(f"OFFSET necessário: x+{offset_x}, y+{offset_y}")
                return True
        elif cmd == 'q':
            break
        else:
            print("Comando inválido!")
    
    return False

def main():
    """Função principal"""
    
    print("=== Calibração de Posição ===")
    print()
    print("Este script vai ajudar a encontrar a posição exata do botão OK")
    print()
    
    input("Pressione Enter para começar...")
    
    test_current_detection()

if __name__ == "__main__":
    main()
