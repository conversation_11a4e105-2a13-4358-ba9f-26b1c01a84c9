"""
Script de instalação e configuração do TailsBot
"""

import subprocess
import sys
import os

def install_package(package):
    """Instala um pacote Python"""
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        return True
    except subprocess.CalledProcessError:
        return False

def check_package(package):
    """Verifica se um pacote está instalado"""
    try:
        __import__(package)
        return True
    except ImportError:
        return False

def main():
    """Função principal de instalação"""
    
    print("=== TailsBot - Instalação e Configuração ===")
    print()
    
    # Lista de pacotes necessários
    packages = [
        ("pyautogui", "pyautogui"),
        ("pandas", "pandas"),
        ("openpyxl", "openpyxl"),
        ("PIL", "Pillow"),
        ("cv2", "opencv-python")
    ]
    
    print("Verificando dependências...")
    
    missing_packages = []
    
    for import_name, package_name in packages:
        if check_package(import_name):
            print(f"✓ {package_name} - OK")
        else:
            print(f"✗ {package_name} - FALTANDO")
            missing_packages.append(package_name)
    
    if missing_packages:
        print(f"\nInstalando {len(missing_packages)} pacote(s) faltante(s)...")
        
        for package in missing_packages:
            print(f"Instalando {package}...")
            if install_package(package):
                print(f"✓ {package} instalado com sucesso")
            else:
                print(f"✗ Falha ao instalar {package}")
    else:
        print("\n✓ Todas as dependências estão instaladas!")
    
    # Verifica arquivos necessários
    print("\nVerificando arquivos...")
    
    required_files = [
        "VerifyWhatsappNumber.py",
        "config.py",
        "WhatsappOKButton.png"
    ]
    
    missing_files = []
    
    for file in required_files:
        if os.path.exists(file):
            print(f"✓ {file} - OK")
        else:
            print(f"✗ {file} - FALTANDO")
            missing_files.append(file)
    
    if missing_files:
        print(f"\n⚠ Arquivos faltando: {', '.join(missing_files)}")
        if "WhatsappOKButton.png" in missing_files:
            print("Execute 'python capture_button.py' para capturar a imagem do botão OK")
    else:
        print("\n✓ Todos os arquivos necessários estão presentes!")
    
    # Testa funcionalidades básicas
    print("\nTestando funcionalidades...")
    
    try:
        import pyautogui
        screen_size = pyautogui.size()
        print(f"✓ PyAutoGUI funcionando - Resolução: {screen_size}")
    except Exception as e:
        print(f"✗ Erro no PyAutoGUI: {e}")
    
    try:
        import cv2
        print(f"✓ OpenCV funcionando - Versão: {cv2.__version__}")
    except Exception as e:
        print(f"✗ OpenCV não disponível: {e}")
        print("  Detecção funcionará sem confidence")
    
    try:
        import pandas as pd
        print(f"✓ Pandas funcionando - Versão: {pd.__version__}")
    except Exception as e:
        print(f"✗ Erro no Pandas: {e}")
    
    # Configuração inicial
    print("\n=== Configuração Inicial ===")
    
    if not os.path.exists("numeros_exemplo.txt"):
        print("Criando arquivo de exemplo para números...")
        with open("numeros_exemplo.txt", "w", encoding="utf-8") as f:
            f.write("# Arquivo de exemplo para números de telefone\n")
            f.write("# Formato: um número por linha, no formato internacional\n")
            f.write("# Exemplo: 5521999999999 (código do país + DDD + número)\n\n")
            f.write("5521965560390\n")
            f.write("5521965560787\n")
        print("✓ Arquivo numeros_exemplo.txt criado")
    
    print("\n=== Próximos Passos ===")
    print("1. Capture a imagem do botão OK:")
    print("   python capture_button.py")
    print()
    print("2. Teste a detecção e clique:")
    print("   python test_click.py")
    print()
    print("3. Configure seus números em:")
    print("   numeros_exemplo.txt")
    print()
    print("4. Execute o programa:")
    print("   python VerifyWhatsappNumber.py")
    print("   ou")
    print("   run_tailsbot.bat")
    
    print("\n✅ Instalação concluída!")

if __name__ == "__main__":
    main()
