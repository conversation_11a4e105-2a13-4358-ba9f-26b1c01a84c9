"""
Sistema de calibração automática para o TailsBot
"""

import json
import os
import pyautogui
import time
import webbrowser
import hashlib
import platform

CALIBRATION_FILE = "calibration_data.json"

def get_machine_id():
    """Gera um ID único para esta máquina"""
    machine_info = f"{platform.node()}-{platform.system()}-{platform.processor()}"
    return hashlib.md5(machine_info.encode()).hexdigest()[:16]

def get_screen_resolution():
    """Obtém a resolução da tela"""
    return pyautogui.size()

def load_calibration():
    """Carrega dados de calibração existentes"""
    if not os.path.exists(CALIBRATION_FILE):
        return None
    
    try:
        with open(CALIBRATION_FILE, 'r') as f:
            data = json.load(f)
        
        machine_id = get_machine_id()
        screen_res = get_screen_resolution()
        
        # Verifica se existe calibração para esta máquina e resolução
        key = f"{machine_id}_{screen_res[0]}x{screen_res[1]}"
        
        if key in data:
            return data[key]
    except:
        pass
    
    return None

def save_calibration(button_x, button_y):
    """Salva dados de calibração"""
    machine_id = get_machine_id()
    screen_res = get_screen_resolution()
    key = f"{machine_id}_{screen_res[0]}x{screen_res[1]}"
    
    # Carrega dados existentes ou cria novo
    data = {}
    if os.path.exists(CALIBRATION_FILE):
        try:
            with open(CALIBRATION_FILE, 'r') as f:
                data = json.load(f)
        except:
            pass
    
    # Adiciona nova calibração
    data[key] = {
        "button_x": button_x,
        "button_y": button_y,
        "machine_id": machine_id,
        "resolution": f"{screen_res[0]}x{screen_res[1]}",
        "calibrated_at": time.strftime("%Y-%m-%d %H:%M:%S")
    }
    
    # Salva
    with open(CALIBRATION_FILE, 'w') as f:
        json.dump(data, f, indent=2)

def run_calibration():
    """Executa o processo de calibração automática"""

    print("=== CALIBRAÇÃO AUTOMÁTICA DO TAILSBOT ===")
    print()
    print("Sistema não calibrado. Iniciando calibração automática...")
    print("Aguarde, o processo é totalmente automático!")
    print()

    # Abre número inválido
    test_number = "5521965560787"
    wa_link = f"https://wa.me/{test_number}"

    print(f"1. Abrindo WhatsApp: {test_number}")
    webbrowser.open(wa_link)

    print("2. Aguardando WhatsApp carregar...")
    time.sleep(8)

    print("3. Procurando botão OK automaticamente...")

    # Procura o botão OK automaticamente
    button_found = False
    button_x, button_y = 0, 0

    # Estratégia 1: Procura por texto "OK" na tela
    try:
        # Tira screenshot para análise
        screenshot = pyautogui.screenshot()

        # Procura por padrões comuns de botão OK
        screen_width, screen_height = pyautogui.size()

        # Posições típicas onde o botão OK aparece (centro da tela)
        center_x = screen_width // 2
        center_y = screen_height // 2

        # Área de busca ao redor do centro
        search_area = 200

        # Procura em uma grade ao redor do centro
        found_positions = []

        for y_offset in range(-search_area, search_area, 20):
            for x_offset in range(-search_area, search_area, 20):
                test_x = center_x + x_offset
                test_y = center_y + y_offset

                # Verifica se está dentro da tela
                if 0 <= test_x < screen_width and 0 <= test_y < screen_height:
                    # Pega a cor do pixel
                    pixel_color = screenshot.getpixel((test_x, test_y))

                    # Procura por cores típicas de botão (azul, cinza claro)
                    r, g, b = pixel_color[:3]

                    # Botão azul do WhatsApp ou cinza claro
                    if ((r < 100 and g < 150 and b > 150) or  # Azul
                        (abs(r - g) < 30 and abs(g - b) < 30 and r > 180)):  # Cinza claro
                        found_positions.append((test_x, test_y))

        if found_positions:
            # Usa a posição mais central
            button_x = sum(pos[0] for pos in found_positions) // len(found_positions)
            button_y = sum(pos[1] for pos in found_positions) // len(found_positions)
            button_found = True
            print(f"   ✓ Botão detectado automaticamente!")

    except Exception as e:
        print(f"   Método automático falhou: {e}")

    # Estratégia 2: Posição padrão baseada na resolução
    if not button_found:
        print("   Usando posição padrão baseada na resolução...")
        screen_width, screen_height = pyautogui.size()

        # Posições típicas do botão OK em diferentes resoluções
        if screen_width >= 1920:  # Full HD ou maior
            button_x = screen_width // 2
            button_y = int(screen_height * 0.6)
        elif screen_width >= 1366:  # HD
            button_x = screen_width // 2
            button_y = int(screen_height * 0.65)
        else:  # Resoluções menores
            button_x = screen_width // 2
            button_y = int(screen_height * 0.7)

        button_found = True
        print(f"   ✓ Usando posição estimada para resolução {screen_width}x{screen_height}")

    print(f"4. Posição calibrada: ({button_x}, {button_y})")

    # Salva calibração
    save_calibration(button_x, button_y)

    print("5. Calibração salva automaticamente!")

    # Testa a calibração
    print("6. Testando calibração...")
    pyautogui.moveTo(button_x, button_y, duration=0.5)
    time.sleep(0.5)

    # Executa clique de teste
    pyautogui.click(button_x, button_y)
    print("   ✓ Clique de teste executado")

    # Fecha a aba
    time.sleep(1)
    pyautogui.hotkey('ctrl', 'w')

    # Mostra alerta de sucesso
    print()
    print("🎉" * 20)
    print("   CALIBRAÇÃO CONCLUÍDA COM SUCESSO!")
    print("   O TailsBot está pronto para uso!")
    print("🎉" * 20)
    print()

    return True

def get_button_position():
    """Obtém a posição calibrada do botão OK"""
    calibration = load_calibration()
    
    if calibration is None:
        print("Calibração necessária...")
        if run_calibration():
            calibration = load_calibration()
        else:
            raise Exception("Falha na calibração")
    
    return calibration["button_x"], calibration["button_y"]

def is_calibrated():
    """Verifica se já existe calibração para esta máquina"""
    return load_calibration() is not None

def force_recalibration():
    """Força uma nova calibração"""
    if os.path.exists(CALIBRATION_FILE):
        # Remove apenas a calibração desta máquina
        machine_id = get_machine_id()
        screen_res = get_screen_resolution()
        key = f"{machine_id}_{screen_res[0]}x{screen_res[1]}"
        
        try:
            with open(CALIBRATION_FILE, 'r') as f:
                data = json.load(f)
            
            if key in data:
                del data[key]
                
                with open(CALIBRATION_FILE, 'w') as f:
                    json.dump(data, f, indent=2)
        except:
            pass
    
    return run_calibration()

if __name__ == "__main__":
    # Teste da calibração
    if is_calibrated():
        print("Máquina já calibrada!")
        x, y = get_button_position()
        print(f"Posição do botão: ({x}, {y})")
    else:
        run_calibration()
