"""
Sistema de calibração automática para o TailsBot
"""

import json
import os
import pyautogui
import time
import webbrowser
import hashlib
import platform

CALIBRATION_FILE = "calibration_data.json"

def get_machine_id():
    """Gera um ID único para esta máquina"""
    machine_info = f"{platform.node()}-{platform.system()}-{platform.processor()}"
    return hashlib.md5(machine_info.encode()).hexdigest()[:16]

def get_screen_resolution():
    """Obtém a resolução da tela"""
    return pyautogui.size()

def load_calibration():
    """Carrega dados de calibração existentes"""
    if not os.path.exists(CALIBRATION_FILE):
        return None
    
    try:
        with open(CALIBRATION_FILE, 'r') as f:
            data = json.load(f)
        
        machine_id = get_machine_id()
        screen_res = get_screen_resolution()
        
        # Verifica se existe calibração para esta máquina e resolução
        key = f"{machine_id}_{screen_res[0]}x{screen_res[1]}"
        
        if key in data:
            return data[key]
    except:
        pass
    
    return None

def save_calibration(button_x, button_y):
    """Salva dados de calibração"""
    machine_id = get_machine_id()
    screen_res = get_screen_resolution()
    key = f"{machine_id}_{screen_res[0]}x{screen_res[1]}"
    
    # Carrega dados existentes ou cria novo
    data = {}
    if os.path.exists(CALIBRATION_FILE):
        try:
            with open(CALIBRATION_FILE, 'r') as f:
                data = json.load(f)
        except:
            pass
    
    # Adiciona nova calibração
    data[key] = {
        "button_x": button_x,
        "button_y": button_y,
        "machine_id": machine_id,
        "resolution": f"{screen_res[0]}x{screen_res[1]}",
        "calibrated_at": time.strftime("%Y-%m-%d %H:%M:%S")
    }
    
    # Salva
    with open(CALIBRATION_FILE, 'w') as f:
        json.dump(data, f, indent=2)

def run_calibration():
    """Executa o processo de calibração"""
    
    print("=== CALIBRAÇÃO DO TAILSBOT ===")
    print()
    print("Esta é a primeira execução nesta máquina/resolução.")
    print("Vamos calibrar a posição do botão OK do WhatsApp.")
    print()
    print("INSTRUÇÕES:")
    print("1. O script vai abrir um número inválido no WhatsApp")
    print("2. Você terá 10 segundos para mover o cursor até o CENTRO do botão OK")
    print("3. Deixe o cursor parado no centro do botão")
    print("4. O script vai gravar a posição automaticamente")
    print()
    
    input("Pressione Enter para começar a calibração...")
    
    # Abre número inválido
    test_number = "5521965560787"
    wa_link = f"https://wa.me/{test_number}"
    
    print(f"Abrindo WhatsApp com número inválido: {test_number}")
    webbrowser.open(wa_link)
    
    print("Aguardando WhatsApp carregar...")
    time.sleep(6)
    
    print()
    print("AGORA:")
    print("- Vá para o navegador")
    print("- Aguarde o botão OK aparecer")
    print("- Mova o cursor para o CENTRO do botão OK")
    print("- Deixe o cursor parado lá")
    print()
    
    # Countdown
    for i in range(10, 0, -1):
        print(f"Gravando posição em {i} segundos... (posicione o cursor no botão OK!)")
        time.sleep(1)
    
    # Grava posição atual do mouse
    button_x, button_y = pyautogui.position()
    
    print(f"✓ Posição gravada: ({button_x}, {button_y})")
    
    # Salva calibração
    save_calibration(button_x, button_y)
    
    print("✓ Calibração salva com sucesso!")
    print()
    
    # Testa a calibração
    print("Testando calibração...")
    pyautogui.moveTo(button_x, button_y, duration=1)
    
    print("O cursor está no centro do botão OK?")
    resposta = input("Digite 's' se estiver correto, ou 'n' para recalibrar: ").lower().strip()
    
    if resposta == 's':
        print("🎉 Calibração concluída com sucesso!")
        
        # Testa clique
        print("Testando clique...")
        pyautogui.click(button_x, button_y)
        
        print("✅ Calibração finalizada! O TailsBot está pronto para uso.")
        return True
    else:
        print("Recalibrando...")
        os.remove(CALIBRATION_FILE)
        return run_calibration()

def get_button_position():
    """Obtém a posição calibrada do botão OK"""
    calibration = load_calibration()
    
    if calibration is None:
        print("Calibração necessária...")
        if run_calibration():
            calibration = load_calibration()
        else:
            raise Exception("Falha na calibração")
    
    return calibration["button_x"], calibration["button_y"]

def is_calibrated():
    """Verifica se já existe calibração para esta máquina"""
    return load_calibration() is not None

def force_recalibration():
    """Força uma nova calibração"""
    if os.path.exists(CALIBRATION_FILE):
        # Remove apenas a calibração desta máquina
        machine_id = get_machine_id()
        screen_res = get_screen_resolution()
        key = f"{machine_id}_{screen_res[0]}x{screen_res[1]}"
        
        try:
            with open(CALIBRATION_FILE, 'r') as f:
                data = json.load(f)
            
            if key in data:
                del data[key]
                
                with open(CALIBRATION_FILE, 'w') as f:
                    json.dump(data, f, indent=2)
        except:
            pass
    
    return run_calibration()

if __name__ == "__main__":
    # Teste da calibração
    if is_calibrated():
        print("Máquina já calibrada!")
        x, y = get_button_position()
        print(f"Posição do botão: ({x}, {y})")
    else:
        run_calibration()
