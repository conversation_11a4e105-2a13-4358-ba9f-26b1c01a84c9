@echo off
echo ========================================
echo    TailsBot - WhatsApp Number Verifier
echo ========================================
echo.

REM Verifica se Python está instalado
python --version >nul 2>&1
if errorlevel 1 (
    echo ERRO: Python não encontrado!
    echo Por favor, instale Python 3.7 ou superior.
    pause
    exit /b 1
)

REM Verifica se o ambiente virtual existe
if not exist "venv\" (
    echo Criando ambiente virtual...
    python -m venv venv
    if errorlevel 1 (
        echo ERRO: Falha ao criar ambiente virtual!
        pause
        exit /b 1
    )
)

REM Ativa o ambiente virtual
echo Ativando ambiente virtual...
call venv\Scripts\activate.bat

REM Instala dependências
echo Instalando dependências...
pip install -r requirements.txt
if errorlevel 1 (
    echo ERRO: Falha ao instalar dependências!
    echo.
    echo Tentando instalar dependências individualmente...
    pip install pyautogui pandas openpyxl Pillow opencv-python
    if errorlevel 1 (
        echo ERRO: Falha ao instalar dependências mesmo individualmente!
        pause
        exit /b 1
    )
)

echo.
echo Iniciando TailsBot...
echo.
python VerifyWhatsappNumber.py

echo.
echo Pressione qualquer tecla para sair...
pause >nul
