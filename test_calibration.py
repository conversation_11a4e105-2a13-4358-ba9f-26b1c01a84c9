"""
Teste simples da calibração
"""

from calibration import is_calibrated, get_button_position, force_recalibration

def main():
    print("=== Teste de Calibração ===")
    print()
    
    if is_calibrated():
        print("✓ Sistema já calibrado!")
        x, y = get_button_position()
        print(f"Posição do botão OK: ({x}, {y})")
        
        print()
        recalibrar = input("Deseja recalibrar? (s/n): ").lower().strip()
        if recalibrar == 's':
            force_recalibration()
    else:
        print("Sistema não calibrado. Execute o programa principal para calibrar.")

if __name__ == "__main__":
    main()
