"""
Script para testar e calibrar o clique no botão OK
"""

import pyautogui
import time
import os
from config import *

def test_button_detection():
    """Testa a detecção do botão OK em tempo real"""
    
    print("=== Teste de Detecção do Botão OK ===")
    print()
    print("INSTRUÇÕES:")
    print("1. Abra o WhatsApp Web")
    print("2. Digite um número SEM WhatsApp (ex: 5521999999999)")
    print("3. Aguarde o botão OK aparecer")
    print("4. Volte para este programa")
    print()
    
    input("Pressione Enter quando o botão OK estiver visível...")
    
    image_path = os.path.join(os.path.dirname(__file__), OK_BUTTON_IMAGE)
    
    if not os.path.exists(image_path):
        print(f"ERRO: Arquivo {OK_BUTTON_IMAGE} não encontrado!")
        return
    
    print("Procurando botão OK...")
    
    # Testa diferentes métodos de detecção
    methods = [
        ("Confidence 0.9", lambda: pyautogui.locateOnScreen(image_path, confidence=0.9)),
        ("Confidence 0.8", lambda: pyautogui.locateOnScreen(image_path, confidence=0.8)),
        ("Confidence 0.7", lambda: pyautogui.locateOnScreen(image_path, confidence=0.7)),
        ("Confidence 0.6", lambda: pyautogui.locateOnScreen(image_path, confidence=0.6)),
        ("Sem confidence", lambda: pyautogui.locateOnScreen(image_path)),
    ]
    
    found_locations = []
    
    for method_name, method_func in methods:
        try:
            location = method_func()
            if location:
                center_x, center_y = pyautogui.center(location)
                print(f"✓ {method_name}: Encontrado em {location}")
                print(f"  Centro: ({center_x}, {center_y})")
                found_locations.append((method_name, location, center_x, center_y))
            else:
                print(f"✗ {method_name}: Não encontrado")
        except Exception as e:
            print(f"✗ {method_name}: Erro - {e}")
    
    if not found_locations:
        print("\n❌ Nenhum método conseguiu detectar o botão!")
        print("Sugestões:")
        print("- Verifique se o botão OK está realmente visível")
        print("- Capture uma nova imagem com capture_button.py")
        print("- Verifique se o zoom do navegador está em 100%")
        return
    
    print(f"\n✅ Botão detectado com {len(found_locations)} método(s)!")
    
    # Testa o clique na melhor detecção
    best_method = found_locations[0]
    method_name, location, center_x, center_y = best_method
    
    print(f"\nTestando clique com: {method_name}")
    print(f"Posição: ({center_x}, {center_y})")
    
    # Salva screenshot antes do clique
    try:
        screenshot = pyautogui.screenshot()
        screenshot.save("before_click.png")
        print("Screenshot salvo: before_click.png")
    except:
        pass
    
    # Move o mouse para mostrar onde vai clicar
    print("Movendo mouse para a posição do botão...")
    pyautogui.moveTo(center_x, center_y, duration=1)
    
    input("Pressione Enter para executar o clique...")
    
    # Executa o clique
    pyautogui.click(center_x, center_y)
    print("Clique executado!")
    
    time.sleep(1)
    
    # Salva screenshot depois do clique
    try:
        screenshot = pyautogui.screenshot()
        screenshot.save("after_click.png")
        print("Screenshot salvo: after_click.png")
    except:
        pass
    
    print("\nVerifique se o clique funcionou!")

def test_multiple_clicks():
    """Testa múltiplos cliques em sequência"""
    
    print("=== Teste de Múltiplos Cliques ===")
    print()
    print("Este teste vai clicar várias vezes no botão OK")
    print("para verificar se algum dos cliques funciona.")
    print()
    
    input("Pressione Enter quando o botão OK estiver visível...")
    
    image_path = os.path.join(os.path.dirname(__file__), OK_BUTTON_IMAGE)
    
    try:
        location = pyautogui.locateOnScreen(image_path, confidence=0.7)
        if not location:
            location = pyautogui.locateOnScreen(image_path)
        
        if location:
            center_x, center_y = pyautogui.center(location)
            print(f"Botão encontrado em: ({center_x}, {center_y})")
            
            # Testa diferentes tipos de clique
            click_methods = [
                ("Clique simples", lambda: pyautogui.click(center_x, center_y)),
                ("Clique duplo", lambda: pyautogui.doubleClick(center_x, center_y)),
                ("Clique com botão esquerdo", lambda: pyautogui.click(center_x, center_y, button='left')),
                ("Clique com pausa", lambda: [pyautogui.mouseDown(center_x, center_y), time.sleep(0.1), pyautogui.mouseUp()]),
            ]
            
            for i, (method_name, click_func) in enumerate(click_methods):
                print(f"\n{i+1}. Testando: {method_name}")
                pyautogui.moveTo(center_x, center_y, duration=0.5)
                time.sleep(0.5)
                
                try:
                    click_func()
                    print(f"   ✓ {method_name} executado")
                except Exception as e:
                    print(f"   ✗ {method_name} falhou: {e}")
                
                time.sleep(2)  # Pausa entre tentativas
                
                # Pergunta se funcionou
                result = input("   Funcionou? (s/n): ").lower().strip()
                if result == 's':
                    print(f"   🎉 Sucesso com: {method_name}")
                    return
            
            print("\n❌ Nenhum método de clique funcionou")
        else:
            print("❌ Botão OK não encontrado!")
            
    except Exception as e:
        print(f"Erro: {e}")

def calibrate_position():
    """Permite ajustar manualmente a posição do clique"""
    
    print("=== Calibração Manual da Posição ===")
    print()
    print("Este modo permite ajustar a posição do clique manualmente")
    print()
    
    input("Pressione Enter quando o botão OK estiver visível...")
    
    image_path = os.path.join(os.path.dirname(__file__), OK_BUTTON_IMAGE)
    
    try:
        location = pyautogui.locateOnScreen(image_path, confidence=0.7)
        if not location:
            location = pyautogui.locateOnScreen(image_path)
        
        if location:
            center_x, center_y = pyautogui.center(location)
            print(f"Posição detectada: ({center_x}, {center_y})")
            
            # Permite ajustar a posição
            while True:
                print(f"\nPosição atual: ({center_x}, {center_y})")
                print("Comandos:")
                print("w/s - mover para cima/baixo")
                print("a/d - mover para esquerda/direita")
                print("t - testar clique")
                print("q - sair")
                
                cmd = input("Comando: ").lower().strip()
                
                if cmd == 'w':
                    center_y -= 5
                elif cmd == 's':
                    center_y += 5
                elif cmd == 'a':
                    center_x -= 5
                elif cmd == 'd':
                    center_x += 5
                elif cmd == 't':
                    pyautogui.moveTo(center_x, center_y, duration=0.5)
                    time.sleep(0.5)
                    pyautogui.click(center_x, center_y)
                    print("Clique executado!")
                elif cmd == 'q':
                    break
                
                # Mostra a nova posição
                if cmd in ['w', 's', 'a', 'd']:
                    pyautogui.moveTo(center_x, center_y, duration=0.3)
        else:
            print("❌ Botão OK não encontrado!")
            
    except Exception as e:
        print(f"Erro: {e}")

def main():
    """Menu principal"""
    
    while True:
        print("\n=== Teste de Clique do Botão OK ===")
        print("1. Testar detecção do botão")
        print("2. Testar múltiplos tipos de clique")
        print("3. Calibração manual da posição")
        print("4. Sair")
        print()
        
        choice = input("Escolha uma opção (1-4): ").strip()
        
        if choice == "1":
            test_button_detection()
        elif choice == "2":
            test_multiple_clicks()
        elif choice == "3":
            calibrate_position()
        elif choice == "4":
            print("Saindo...")
            break
        else:
            print("Opção inválida!")

if __name__ == "__main__":
    main()
