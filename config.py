"""
Configurações do TailsBot - WhatsApp Number Verifier
"""

# Configurações de tempo (em segundos)
WAIT_TIME_AFTER_OPEN = 5  # Tempo de espera após abrir o link do WhatsApp (aumentado)
WAIT_TIME_BETWEEN_ATTEMPTS = 1.0  # Tempo entre tentativas de encontrar o botão (aumentado)
WAIT_TIME_BEFORE_CLOSE = 1.5  # Tempo antes de fechar a aba (aumentado)
WAIT_TIME_MOUSE_MOVE = 0.3  # Tempo após mover o mouse (aumentado)

# Configurações de detecção de imagem
IMAGE_CONFIDENCE = 0.8  # Confiança para detecção do botão OK (reduzido para ser menos restritivo)
MAX_ATTEMPTS = 3  # Número máximo de tentativas para encontrar o botão (aumentado)

# Configurações de arquivo
OUTPUT_FILE = 'resultados_whatsapp.xlsx'  # Nome do arquivo de saída
SAVE_INTERVAL = 500  # Salvar resultados a cada X números processados

# Configurações de planilha
VALID_SHEET_NAME = 'Válidos'
INVALID_SHEET_NAME = 'Inválidos'
VALID_COLUMN_NAME = 'Números com WhatsApp'
INVALID_COLUMN_NAME = 'Números sem WhatsApp'

# Imagem do botão OK
OK_BUTTON_IMAGE = 'WhatsappOKButton.png'

# Lista de números de exemplo (substitua pelos seus números)
EXAMPLE_PHONE_NUMBERS = [
    '5521965560390',
    '5521965560787', 
    '5521965561448',
    '5521980244100'
]

# Configurações de log
LOG_ENABLED = True
LOG_FILE = 'whatsapp_verifier.log'
