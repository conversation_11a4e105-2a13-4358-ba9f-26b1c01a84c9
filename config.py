"""
Configurações do TailsBot - WhatsApp Number Verifier
"""

# Configurações de tempo (em segundos)
WAIT_TIME_AFTER_OPEN = 6  # Tempo de espera após abrir o link do WhatsApp
WAIT_TIME_BETWEEN_ATTEMPTS = 1.5  # Tempo entre tentativas de encontrar o botão
WAIT_TIME_BEFORE_CLOSE = 2.0  # Tempo antes de fechar a aba
WAIT_TIME_MOUSE_MOVE = 0.5  # Tempo após mover o mouse

# Configurações de detecção de imagem
IMAGE_CONFIDENCE = 0.7  # Confiança para detecção do botão OK (mais permissivo)
MAX_ATTEMPTS = 4  # Número máximo de tentativas para encontrar o botão

# Configurações de clique
CLICK_DURATION = 0.1  # Duração do clique (tempo pressionado)
MULTIPLE_CLICKS = True  # Se deve tentar múltiplos cliques
CLICK_ATTEMPTS = 3  # Número de tentativas de clique

# Configurações de arquivo
OUTPUT_FILE = 'resultados_whatsapp.xlsx'  # Nome do arquivo de saída
SAVE_INTERVAL = 500  # Salvar resultados a cada X números processados

# Configurações de planilha
VALID_SHEET_NAME = 'Válidos'
INVALID_SHEET_NAME = 'Inválidos'
VALID_COLUMN_NAME = 'Números com WhatsApp'
INVALID_COLUMN_NAME = 'Números sem WhatsApp'

# Imagem do botão OK
OK_BUTTON_IMAGE = 'WhatsappOKButton.png'

# Lista de números de exemplo (substitua pelos seus números)
EXAMPLE_PHONE_NUMBERS = [
    '5521965560390',
    '5521965560787', 
    '5521965561448',
    '5521980244100'
]

# Configurações de log
LOG_ENABLED = True
LOG_FILE = 'whatsapp_verifier.log'
