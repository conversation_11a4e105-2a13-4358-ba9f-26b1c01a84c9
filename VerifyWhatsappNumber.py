import pyautogui
import time
import os
import pandas as pd
import webbrowser

# Função para obter o caminho absoluto do arquivo de imagem
def get_image_path(image_name):
    base_path = os.path.dirname(os.path.abspath(__file__))
    return os.path.join(base_path, image_name)

# Função para alternar para a janela do WhatsApp e garantir que está em foco
def focus_whatsapp_window():
    pyautogui.hotkey('alt', 'tab')  # Alterna entre as janelas para garantir que o WhatsApp esteja em foco    

# Função para verificar se o número tem WhatsApp
def check_whatsapp_number(phone_number):
    wa_link = f"https://wa.me/{phone_number}"

    webbrowser.open(wa_link)
    time.sleep(3)  # Aguarda 3 segundos para o WhatsApp carregar

    image_path = get_image_path('WhatsappOKButton.png')
    button_found = False
    
    for i in range(2):  # Tentativa 2 vezes
        try:
            # Força o foco na janela do WhatsApp antes de procurar o botão
            focus_whatsapp_window()
            time.sleep(0.5)

            # Procura o botão "OK" na tela inteira
            location = pyautogui.locateOnScreen(image_path, confidence=0.85)
            if location:
                print(f"Número {phone_number} não possui WhatsApp.")
                print(f"Botão OK encontrado em: {location}")  # Imprime as coordenadas do botão "OK"

                # Move o mouse para o centro do botão e clica
                x, y = pyautogui.center(location)  # Obtém o centro da área do botão
                pyautogui.moveTo(x, y)  # Move o mouse para o botão
                time.sleep(0.2)  # Pequena pausa para garantir que o mouse foi movido
                pyautogui.click()  # Executa o clique no botão "OK"

                button_found = True
                time.sleep(1)  # Espera 1 segundo antes de fechar a aba
                focus_whatsapp_window()
                pyautogui.hotkey('ctrl', 'w')  # Fecha a aba do navegador
                return False
        except pyautogui.ImageNotFoundException:
            print(f"Botão 'OK' não encontrado na tentativa {i+1}.")
        except Exception as e:
            print(f"Erro ao verificar o número: {e}")
        
        time.sleep(0.5)  # Pausa entre as tentativas

    if not button_found:
        print(f"Número {phone_number} possui WhatsApp ou o botão 'OK' não foi detectado.")
        focus_whatsapp_window()
        pyautogui.hotkey('ctrl', 'w')
        return True

# Função para processar a lista de números de telefone
def process_phone_numbers(phone_numbers, start_index=0):
    valid_numbers = []
    invalid_numbers = []
    
    # Carregar resultados parciais, se já existirem
    try:
        resultados = pd.read_excel('resultados_whatsapp.xlsx', sheet_name=None)
        valid_numbers = resultados.get('Válidos', pd.DataFrame()).squeeze().tolist()
        invalid_numbers = resultados.get('Inválidos', pd.DataFrame()).squeeze().tolist()
    except FileNotFoundError:
        print("Nenhum arquivo de resultados encontrado. Começando novo processamento.")

    # Processa a partir do índice fornecido
    for i, number in enumerate(phone_numbers[start_index:], start=start_index):
        if check_whatsapp_number(number):
            valid_numbers.append(number)
        else:
            invalid_numbers.append(number)

        # Salva os resultados a cada 500 números processados
        if (i + 1) % 500 == 0:
            generate_excel(valid_numbers, invalid_numbers)
            print(f"Salvando resultados parciais no arquivo 'resultados_whatsapp.xlsx'.")
    
    return valid_numbers, invalid_numbers

# Função para gerar ou atualizar a planilha Excel
def generate_excel(valid_numbers, invalid_numbers):
    valid_df = pd.DataFrame({'Números com WhatsApp': valid_numbers})
    invalid_df = pd.DataFrame({'Números sem WhatsApp': invalid_numbers})

    with pd.ExcelWriter('resultados_whatsapp.xlsx', mode='w') as writer:
        valid_df.to_excel(writer, sheet_name='Válidos', index=False)
        invalid_df.to_excel(writer, sheet_name='Inválidos', index=False)

# Lista de números de telefone para testar (exemplo)
phone_numbers = ['5521965560390','5521965560787','5521965561448','5521980244100']

# Começa o processamento a partir do índice desejado (exemplo: 699)
start_index = 0  # Substitua pelo índice desejado

# Processa a lista de números a partir do índice escolhido
valid_numbers, invalid_numbers = process_phone_numbers(phone_numbers, start_index)

# Gera o arquivo Excel com os números restantes
generate_excel(valid_numbers, invalid_numbers)

print("Processo concluído! Verifique o arquivo 'resultados_whatsapp.xlsx'.")
