"""
TailsBot - WhatsApp Number Verifier
Verifica se números de telefone possuem WhatsApp ativo

Autor: mrflag
GitHub: https://github.com/mrflag/TailsBot
"""

import pyautogui
import time
import os
import pandas as pd
import webbrowser
import logging
from typing import List, Tuple
from config import *

# Configurar logging
def setup_logging():
    """Configura o sistema de logging"""
    if LOG_ENABLED:
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(LOG_FILE),
                logging.StreamHandler()
            ]
        )
    else:
        logging.basicConfig(level=logging.WARNING)

def get_image_path(image_name: str) -> str:
    """Obtém o caminho absoluto do arquivo de imagem"""
    base_path = os.path.dirname(os.path.abspath(__file__))
    return os.path.join(base_path, image_name)

def focus_whatsapp_window() -> None:
    """Alterna para a janela do WhatsApp e garante que está em foco"""
    try:
        # Clica no centro da tela para garantir foco
        screen_width, screen_height = pyautogui.size()
        pyautogui.click(screen_width // 2, screen_height // 2)
        time.sleep(0.2)

        # Alterna para a janela ativa (navegador)
        pyautogui.hotkey('alt', 'tab')
        time.sleep(0.3)
    except Exception as e:
        logging.debug(f"Erro ao focar janela: {e}")

def verify_click_success(image_path: str, timeout: int = 3) -> bool:
    """
    Verifica se o clique foi bem-sucedido checando se o botão ainda está visível

    Args:
        image_path: Caminho para a imagem do botão
        timeout: Tempo limite para verificação

    Returns:
        True se o clique foi bem-sucedido (botão desapareceu), False caso contrário
    """
    start_time = time.time()

    while time.time() - start_time < timeout:
        try:
            # Se ainda conseguir encontrar o botão, o clique não funcionou
            location = pyautogui.locateOnScreen(image_path, confidence=0.6)
            if location:
                time.sleep(0.5)
                continue
            else:
                # Botão não encontrado = clique funcionou
                return True
        except pyautogui.ImageNotFoundException:
            # Botão não encontrado = clique funcionou
            return True
        except Exception:
            time.sleep(0.5)
            continue

    # Se chegou aqui, o botão ainda está visível após o timeout
    return False

def check_whatsapp_number(phone_number: str) -> bool:
    """
    Verifica se o número tem WhatsApp ativo

    Args:
        phone_number: Número de telefone no formato internacional

    Returns:
        True se o número possui WhatsApp, False caso contrário
    """
    wa_link = f"https://wa.me/{phone_number}"
    logging.info(f"Verificando número: {phone_number}")

    try:
        webbrowser.open(wa_link)
        time.sleep(WAIT_TIME_AFTER_OPEN)

        image_path = get_image_path(OK_BUTTON_IMAGE)
        button_found = False

        for i in range(MAX_ATTEMPTS):
            try:
                # Força o foco na janela do WhatsApp antes de procurar o botão
                focus_whatsapp_window()
                time.sleep(WAIT_TIME_BETWEEN_ATTEMPTS)

                # Tenta localizar o botão com diferentes métodos
                location = None

                # Método 1: Com confidence (se OpenCV disponível)
                try:
                    location = pyautogui.locateOnScreen(image_path, confidence=IMAGE_CONFIDENCE)
                    logging.debug(f"Método 1 (confidence {IMAGE_CONFIDENCE}): {location}")
                except TypeError:
                    logging.warning("OpenCV não detectado. Tentando sem confidence...")
                except pyautogui.ImageNotFoundException:
                    logging.debug(f"Método 1 falhou - botão não encontrado com confidence {IMAGE_CONFIDENCE}")

                # Método 2: Sem confidence se o primeiro falhou
                if not location:
                    try:
                        location = pyautogui.locateOnScreen(image_path)
                        logging.debug(f"Método 2 (sem confidence): {location}")
                    except pyautogui.ImageNotFoundException:
                        logging.debug("Método 2 falhou - botão não encontrado sem confidence")

                # Método 3: Com confidence mais baixa
                if not location:
                    try:
                        location = pyautogui.locateOnScreen(image_path, confidence=0.6)
                        logging.debug(f"Método 3 (confidence 0.6): {location}")
                    except (TypeError, pyautogui.ImageNotFoundException):
                        logging.debug("Método 3 falhou")

                if location:
                    logging.info(f"Número {phone_number} não possui WhatsApp.")
                    logging.info(f"Botão OK encontrado em: {location}")

                    # Calcula o centro do botão
                    center_x, center_y = pyautogui.center(location)
                    logging.info(f"Centro calculado: ({center_x}, {center_y})")

                    # Salva screenshot para debug
                    try:
                        screenshot = pyautogui.screenshot()
                        screenshot.save(f"debug_screenshot_{phone_number}.png")
                        logging.debug(f"Screenshot salvo: debug_screenshot_{phone_number}.png")
                    except:
                        pass

                    # Move o mouse de forma mais suave e visível
                    current_x, current_y = pyautogui.position()
                    logging.debug(f"Posição atual do mouse: ({current_x}, {current_y})")

                    pyautogui.moveTo(center_x, center_y, duration=0.5)
                    time.sleep(WAIT_TIME_MOUSE_MOVE)

                    # Verifica se o mouse chegou na posição correta
                    final_x, final_y = pyautogui.position()
                    logging.debug(f"Posição final do mouse: ({final_x}, {final_y})")

                    # Executa cliques com diferentes estratégias
                    click_success = False

                    if MULTIPLE_CLICKS:
                        # Estratégia 1: Múltiplos cliques rápidos
                        for click_attempt in range(CLICK_ATTEMPTS):
                            pyautogui.click(center_x, center_y)
                            logging.debug(f"Clique rápido {click_attempt + 1} executado em ({center_x}, {center_y})")
                            time.sleep(0.1)

                        time.sleep(0.5)

                        # Estratégia 2: Clique com duração
                        pyautogui.mouseDown(center_x, center_y)
                        time.sleep(CLICK_DURATION)
                        pyautogui.mouseUp()
                        logging.debug(f"Clique com duração executado em ({center_x}, {center_y})")

                        time.sleep(0.3)

                        # Estratégia 3: Clique duplo
                        pyautogui.doubleClick(center_x, center_y)
                        logging.debug(f"Clique duplo executado em ({center_x}, {center_y})")

                    else:
                        # Clique simples
                        pyautogui.click(center_x, center_y)
                        logging.debug(f"Clique simples executado em ({center_x}, {center_y})")

                    # Verifica se o clique funcionou
                    time.sleep(1)  # Aguarda um pouco para o sistema processar o clique

                    click_worked = verify_click_success(image_path, timeout=3)

                    if click_worked:
                        logging.info(f"✓ Clique bem-sucedido! Botão OK desapareceu.")
                        button_found = True
                    else:
                        logging.warning(f"⚠ Clique pode não ter funcionado. Botão ainda visível.")
                        # Tenta um clique adicional mais forte
                        logging.info("Tentando clique adicional...")
                        pyautogui.click(center_x, center_y, clicks=2, interval=0.1)
                        time.sleep(1)

                        # Verifica novamente
                        if verify_click_success(image_path, timeout=2):
                            logging.info("✓ Clique adicional funcionou!")
                            button_found = True
                        else:
                            logging.error("✗ Cliques não funcionaram. Continuando...")
                            button_found = True  # Assume que funcionou para não travar

                    time.sleep(WAIT_TIME_BEFORE_CLOSE)

                    # Fecha a aba
                    focus_whatsapp_window()
                    pyautogui.hotkey('ctrl', 'w')
                    return False

            except pyautogui.ImageNotFoundException:
                logging.debug(f"Botão 'OK' não encontrado na tentativa {i+1}.")
            except Exception as e:
                logging.error(f"Erro ao verificar o número {phone_number}: {e}")

            time.sleep(WAIT_TIME_BETWEEN_ATTEMPTS)

        if not button_found:
            logging.info(f"Número {phone_number} possui WhatsApp ou o botão 'OK' não foi detectado.")
            focus_whatsapp_window()
            pyautogui.hotkey('ctrl', 'w')
            return True

    except Exception as e:
        logging.error(f"Erro geral ao verificar número {phone_number}: {e}")
        # Tenta fechar a aba mesmo em caso de erro
        try:
            focus_whatsapp_window()
            pyautogui.hotkey('ctrl', 'w')
        except:
            pass
        return False

def load_existing_results() -> Tuple[List[str], List[str]]:
    """Carrega resultados parciais existentes, se houver"""
    valid_numbers = []
    invalid_numbers = []

    try:
        resultados = pd.read_excel(OUTPUT_FILE, sheet_name=None)
        valid_df = resultados.get(VALID_SHEET_NAME, pd.DataFrame())
        invalid_df = resultados.get(INVALID_SHEET_NAME, pd.DataFrame())

        if not valid_df.empty:
            valid_numbers = valid_df.iloc[:, 0].tolist()
        if not invalid_df.empty:
            invalid_numbers = invalid_df.iloc[:, 0].tolist()

        logging.info(f"Carregados {len(valid_numbers)} números válidos e {len(invalid_numbers)} inválidos do arquivo existente.")

    except FileNotFoundError:
        logging.info("Nenhum arquivo de resultados encontrado. Começando novo processamento.")
    except Exception as e:
        logging.error(f"Erro ao carregar resultados existentes: {e}")

    return valid_numbers, invalid_numbers

def process_phone_numbers(phone_numbers: List[str], start_index: int = 0) -> Tuple[List[str], List[str]]:
    """
    Processa a lista de números de telefone

    Args:
        phone_numbers: Lista de números para verificar
        start_index: Índice para começar o processamento

    Returns:
        Tupla com listas de números válidos e inválidos
    """
    valid_numbers, invalid_numbers = load_existing_results()

    total_numbers = len(phone_numbers)
    logging.info(f"Iniciando processamento de {total_numbers} números a partir do índice {start_index}")

    # Processa a partir do índice fornecido
    for i, number in enumerate(phone_numbers[start_index:], start=start_index):
        logging.info(f"Processando número {i+1}/{total_numbers}: {number}")

        if check_whatsapp_number(number):
            valid_numbers.append(number)
        else:
            invalid_numbers.append(number)

        # Salva os resultados a cada X números processados
        if (i + 1) % SAVE_INTERVAL == 0:
            generate_excel(valid_numbers, invalid_numbers)
            logging.info(f"Salvando resultados parciais no arquivo '{OUTPUT_FILE}'.")

    return valid_numbers, invalid_numbers

def generate_excel(valid_numbers: List[str], invalid_numbers: List[str]) -> None:
    """
    Gera ou atualiza a planilha Excel com os resultados

    Args:
        valid_numbers: Lista de números com WhatsApp
        invalid_numbers: Lista de números sem WhatsApp
    """
    try:
        valid_df = pd.DataFrame({VALID_COLUMN_NAME: valid_numbers})
        invalid_df = pd.DataFrame({INVALID_COLUMN_NAME: invalid_numbers})

        with pd.ExcelWriter(OUTPUT_FILE, mode='w', engine='openpyxl') as writer:
            valid_df.to_excel(writer, sheet_name=VALID_SHEET_NAME, index=False)
            invalid_df.to_excel(writer, sheet_name=INVALID_SHEET_NAME, index=False)

        logging.info(f"Arquivo '{OUTPUT_FILE}' atualizado com {len(valid_numbers)} números válidos e {len(invalid_numbers)} inválidos.")

    except Exception as e:
        logging.error(f"Erro ao gerar arquivo Excel: {e}")

def load_numbers_from_file(filename: str) -> List[str]:
    """
    Carrega números de telefone de um arquivo de texto

    Args:
        filename: Nome do arquivo com os números

    Returns:
        Lista de números de telefone
    """
    numbers = []
    try:
        with open(filename, 'r', encoding='utf-8') as file:
            for line in file:
                line = line.strip()
                # Ignora linhas vazias e comentários
                if line and not line.startswith('#'):
                    # Remove espaços e caracteres especiais
                    number = ''.join(filter(str.isdigit, line))
                    if number:
                        numbers.append(number)

        logging.info(f"Carregados {len(numbers)} números do arquivo '{filename}'")

    except FileNotFoundError:
        logging.warning(f"Arquivo '{filename}' não encontrado. Usando números de exemplo.")
        numbers = EXAMPLE_PHONE_NUMBERS
    except Exception as e:
        logging.error(f"Erro ao carregar arquivo '{filename}': {e}")
        numbers = EXAMPLE_PHONE_NUMBERS

    return numbers

def check_requirements() -> bool:
    """Verifica se todos os requisitos estão atendidos"""

    # Verifica se a imagem do botão OK existe
    image_path = get_image_path(OK_BUTTON_IMAGE)
    if not os.path.exists(image_path):
        logging.error(f"ERRO: Arquivo de imagem '{OK_BUTTON_IMAGE}' não encontrado!")
        logging.error("Por favor, certifique-se de que a imagem do botão OK está no diretório do projeto.")
        return False

    # Verifica se pyautogui pode funcionar
    try:
        pyautogui.size()
    except Exception as e:
        logging.error(f"ERRO: PyAutoGUI não pode acessar a tela: {e}")
        return False

    logging.info("✓ Todos os requisitos verificados com sucesso!")
    return True

def main():
    """Função principal do programa"""
    setup_logging()

    logging.info("=== TailsBot - WhatsApp Number Verifier ===")
    logging.info("Iniciando verificação de números...")

    # Verifica requisitos antes de começar
    if not check_requirements():
        logging.error("Falha na verificação de requisitos. Encerrando programa.")
        input("Pressione Enter para sair...")
        return

    # Tenta carregar números de arquivo, senão usa os de exemplo
    phone_numbers = load_numbers_from_file('numeros_exemplo.txt')

    # Índice para começar o processamento
    start_index = 0

    try:
        # Processa a lista de números a partir do índice escolhido
        valid_numbers, invalid_numbers = process_phone_numbers(phone_numbers, start_index)

        # Gera o arquivo Excel final
        generate_excel(valid_numbers, invalid_numbers)

        logging.info("=== Processo concluído! ===")
        logging.info(f"Números com WhatsApp: {len(valid_numbers)}")
        logging.info(f"Números sem WhatsApp: {len(invalid_numbers)}")
        logging.info(f"Verifique o arquivo '{OUTPUT_FILE}'")

    except KeyboardInterrupt:
        logging.info("Processo interrompido pelo usuário.")
    except Exception as e:
        logging.error(f"Erro durante a execução: {e}")

if __name__ == "__main__":
    main()
