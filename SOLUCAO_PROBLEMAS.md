# 🔧 Solução de Problemas - TailsBot

## ❌ Problema: "The confidence keyword argument is only available if OpenCV is installed"

### Solução:
1. **Instale o OpenCV:**
```bash
pip install opencv-python
```

2. **Ou execute o script de instalação:**
```bash
run_tailsbot.bat
```

---

## ❌ Problema: Botão OK não está sendo clicado

### Possíveis Causas e Soluções:

### 1. **Imagem do botão OK incorreta ou desatualizada**

**Solução:**
1. Execute o script de captura:
```bash
python capture_button.py
```

2. <PERSON><PERSON> as instruções para capturar uma nova imagem do botão OK

3. Certifique-se de que a imagem `WhatsappOKButton.png` está no diretório do projeto

### 2. **Resolução da tela ou zoom do navegador**

**Solução:**
- Certifique-se de que o zoom do navegador está em 100%
- Use sempre a mesma resolução de tela
- Se mudou a resolução, capture uma nova imagem do botão

### 3. **Configurações de detecção muito restritivas**

**Solução:**
Edite o arquivo `config.py` e ajuste:

```python
# Reduza a confiança para ser menos restritivo
IMAGE_CONFIDENCE = 0.7  # Era 0.8

# Aumente o número de tentativas
MAX_ATTEMPTS = 5  # Era 3

# Aumente os tempos de espera
WAIT_TIME_AFTER_OPEN = 7  # Era 5
WAIT_TIME_BETWEEN_ATTEMPTS = 1.5  # Era 1.0
```

### 4. **Problema de foco da janela**

**Solução:**
- Mantenha apenas uma aba do navegador aberta
- Não mova o mouse durante a execução
- Certifique-se de que o WhatsApp Web está logado

---

## 🧪 Como Testar se Está Funcionando

### 1. **Teste a detecção do botão:**
```bash
python capture_button.py
# Escolha opção 2 para testar
```

### 2. **Teste com um número conhecido sem WhatsApp:**
- Edite `numeros_exemplo.txt`
- Coloque apenas um número que você sabe que não tem WhatsApp
- Execute o programa

### 3. **Verifique os logs:**
- Abra o arquivo `whatsapp_verifier.log`
- Procure por mensagens de erro específicas

---

## 📋 Checklist de Verificação

Antes de executar o programa, verifique:

- [ ] Python 3.7+ instalado
- [ ] Todas as dependências instaladas (`pip install -r requirements.txt`)
- [ ] OpenCV instalado (`pip install opencv-python`)
- [ ] Arquivo `WhatsappOKButton.png` existe no diretório
- [ ] WhatsApp Web logado no navegador
- [ ] Zoom do navegador em 100%
- [ ] Apenas uma aba do navegador aberta
- [ ] Números no formato correto em `numeros_exemplo.txt`

---

## 🆘 Ainda Não Funciona?

### Modo Debug:

1. **Ative logs mais detalhados:**
Edite `config.py`:
```python
LOG_ENABLED = True
```

2. **Execute em modo de teste:**
```bash
python capture_button.py
```

3. **Verifique manualmente:**
- Abra um número sem WhatsApp no navegador
- Veja se o botão OK aparece
- Compare com a imagem `WhatsappOKButton.png`

### Contato:
Se ainda assim não funcionar, abra uma issue no GitHub com:
- Sistema operacional
- Versão do Python
- Logs de erro completos
- Screenshot do botão OK que aparece na sua tela

---

## 💡 Dicas Importantes

1. **Sempre use o mesmo navegador e configurações**
2. **Mantenha o WhatsApp Web sempre logado**
3. **Não minimize o navegador durante a execução**
4. **Execute o programa em horários de menor uso da internet**
5. **Teste primeiro com poucos números**
